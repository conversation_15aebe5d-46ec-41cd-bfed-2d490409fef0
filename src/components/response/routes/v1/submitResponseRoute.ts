import { Router } from 'express';
import { ExtractOriginFromRequest, ExtractIPAddressFromOrigin, ExtractCountryFromIPAddress, ExtractRegionFromIPAddress } from '../../../../global/middlewares';
import { validateSubmitResponsePayload, verifySurveyExists, BlockRequestBySurveyTypeMismatch } from '../../middlewares';
import { submitResponseController } from '../../controllers';
import { Var } from '../../../../global/var';

export const submitResponseRoute = Router();

submitResponseRoute.post(
  `/${Var.app.api.version}/responses`,
  ExtractOriginFromRequest,
  ExtractIPAddressFromOrigin,
  ExtractCountryFromIPAddress,
  ExtractRegionFromIPAddress,
  validateSubmitResponsePayload,
  verifySurveyExists,
  BlockRequestBySurveyTypeMismatch,
  submitResponseController,
);
