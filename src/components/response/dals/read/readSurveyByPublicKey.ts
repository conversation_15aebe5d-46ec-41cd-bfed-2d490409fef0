import { surveyModel } from '../../../survey/models';
import { Var } from '../../../../global/var';
import { Op } from 'sequelize';

export interface SurveyData {
  id: string;
  type: string;
  title: string;
  distribution: string;
  embed_url: string;
  config: Record<string, any>;
  respondent_details: Array<any>;
  public_key: string;
}

const REQUIRED_ATTRIBUTES = ['id', 'public_key', 'type', 'distribution', 'embed_url', 'config', 'respondent_details'];

export const readSurveyByPublicKey = async (publicKey: string): Promise<SurveyData | null> => {
  if (!publicKey) {
    return null;
  }

  try {
    const survey = await surveyModel.findOne({
      attributes: REQUIRED_ATTRIBUTES,
      where: {
        public_key: publicKey,
        [Op.and]: [{ is_deleted: false }, { is_disabled: false }],
      },
    });

    if (!survey) {
      return null;
    }

    const surveyData = survey.toJSON() as SurveyData;

    // Ensure public_key is set
    if (!surveyData.public_key) {
      if (Var.node.env === 'dev') {
        console.error(`${Var.app.emoji.failure} Survey data missing public_key:`, surveyData);
      }
      surveyData.public_key = publicKey;
    }

    if (Var.node.env === 'dev') {
      console.log(`${Var.app.emoji.success} Survey data from database:`, surveyData);
    }

    return surveyData;
  } catch (error) {
    if (Var.node.env === 'dev') {
      console.error(`${Var.app.emoji.failure} Error fetching survey by public key:`, error);
    }
    throw error;
  }
};
