import { DataTypes, ModelAttributes, ModelOptions } from 'sequelize';
import { Sequelize } from '../../../global/var';

const modelName: string = 'survey';

const modelAttributes: ModelAttributes = {
  meta: {
    type: DataTypes.JSONB,
    allowNull: true,
    defaultValue: {},
  },
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    allowNull: false,
    unique: true,
    primaryKey: true,
  },
  type: {
    type: DataTypes.STRING,
    allowNull: false,
  },
  title: {
    type: DataTypes.STRING,
    allowNull: false,
  },
  distribution: {
    type: DataTypes.STRING,
    allowNull: false,
  },
  embed_url: {
    type: DataTypes.STRING,
    allowNull: true,
    validate: {
      isHttpsOrLocalhost(value: string) {
        if (value && !/^https:\/\//.test(value) && !/^http:\/\/localhost/.test(value) && !/^http:\/\/127\.0\.0\.1/.test(value)) {
          throw new Error("Embed URL must start with 'https://' or be a localhost/127.0.0.1 URL");
        }
      },
    },
  },
  tags: {
    type: DataTypes.ARRAY(DataTypes.STRING),
    allowNull: true,
    defaultValue: [],
  },
  config: {
    type: DataTypes.JSON,
    allowNull: false,
    defaultValue: {},
  },
  respondent_details: {
    type: DataTypes.JSONB,
    allowNull: true,
    defaultValue: [],
  },
  public_key: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    allowNull: false,
    unique: true,
  },
  share_key: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    allowNull: false,
    unique: true,
  },
  response_count: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
  },
  expiry: {
    type: DataTypes.INTEGER,
    defaultValue: -1,
  },
  is_disabled: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
  },
  is_deleted: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
  },
};

const modelOptions: ModelOptions = {
  tableName: 'surveys',
  timestamps: true, // Enable timestamps to use created_at and updated_at columns
  underscored: true, // Use snake_case for column names
  freezeTableName: true,
};

export const surveyModel = Sequelize.define(modelName, modelAttributes, modelOptions);
