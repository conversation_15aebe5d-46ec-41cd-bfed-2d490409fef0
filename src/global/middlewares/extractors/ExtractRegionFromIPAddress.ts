import { Request, Response, NextFunction } from 'express';
import geoip from 'geoip-lite';
import { Var } from '../../var';

const regionCache: Map<string, string | null> = new Map();

export const ExtractRegionFromIPAddress = (req: Request, res: Response, next: NextFunction) => {
  try {
    const ip = res.locals.clientIPAddress;

    if (!ip) {
      return next();
    }

    if (regionCache.has(ip)) {
      res.locals.region = regionCache.get(ip);

      if (Var.node.env === 'dev') {
        console.log(`${Var.app.emoji.success} Region from cache for IP ${ip}: ${res.locals.region || 'unknown'}`);
      }
      return next();
    }

    const geoData = geoip.lookup(ip);
    let region: string | null = null;

    if (geoData && geoData.region) {
      region = geoData.region;
    }

    regionCache.set(ip, region);
    res.locals.region = region;

    if (Var.node.env === 'dev') {
      console.log(`${Var.app.emoji.success} Extracted region for IP ${ip}: ${region || 'unknown'}`);
    }
    next();
  } catch (error) {
    console.error(`${Var.app.emoji.failure} Error extracting region from IP:`, error);
    next();
  }
};
