import { Request, Response, NextFunction } from 'express';
import { Var } from '../../var';

export const ExtractOriginFromRequest = (req: Request, res: Response, next: NextFunction) => {
  try {
    res.locals.origin = req.header('Origin') || '';

    if (Var.node.env === 'dev') {
      console.log(`${Var.app.emoji.success} Request origin: ${res.locals.origin}`);
    }

    next();
  } catch (error) {
    console.error(`${Var.app.emoji.failure} Error extracting origin from request:`, error);

    res.locals.origin = '';
    next();
  }
};
