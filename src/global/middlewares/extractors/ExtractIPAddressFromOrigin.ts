import { Request, Response, NextFunction } from 'express';
import { Var } from '../../var';

export const ExtractIPAddressFromOrigin = (req: Request, res: Response, next: NextFunction) => {
  try {
    const ip: string = ((req.headers['x-forwarded-for'] as string) || req.socket.remoteAddress || '').split(',')[0].trim(); // Handle potential comma-separated list

    if (!ip) {
      res.locals.clientIPAddress = '127.0.0.1';

      if (Var.node.env === 'dev') {
        console.log(`${Var.app.emoji.warning} No IP found, using default: 127.0.0.1`);
      }

      return next();
    }

    res.locals.clientIPAddress = ip;

    if (Var.node.env === 'dev') {
      console.log(`${Var.app.emoji.success} Origin IP: ${res.locals.clientIPAddress}`);
    }

    next();
  } catch (error) {
    console.error(`${Var.app.emoji.failure} Error extracting IP address:`, error);

    res.locals.clientIPAddress = '127.0.0.1';
    next();
  }
};
