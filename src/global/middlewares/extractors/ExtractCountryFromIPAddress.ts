import { Request, Response, NextFunction } from 'express';
import geoip from 'geoip-lite';
import { Var } from '../../var';

const countryCache: Map<string, any> = new Map();

export const ExtractCountryFromIPAddress = (req: Request, res: Response, next: NextFunction) => {
  try {
    const ip = res.locals.clientIPAddress;

    if (!ip) {
      return next();
    }

    let originCountry: any;

    if (countryCache.has(ip)) {
      originCountry = countryCache.get(ip);
    } else {
      originCountry = geoip.lookup(ip);
      if (originCountry) {
        countryCache.set(ip, originCountry);
      }
    }

    if (!originCountry) {
      if (Var.node.env === 'dev') {
        console.log(`${Var.app.emoji.warning} Unable to ascertain origin country for IP: ${ip}`);
      }
      res.locals.country = undefined;
      return next();
    }

    res.locals.country = typeof originCountry === 'string' ? originCountry : originCountry.country;

    if (Var.node.env === 'dev') {
      console.log(`${Var.app.emoji.success} Origin country for IP ${ip}: ${res.locals.country}`);
    }

    next();
  } catch (error) {
    console.error(`${Var.app.emoji.failure} Error extracting country from IP:`, error);

    next();
  }
};
