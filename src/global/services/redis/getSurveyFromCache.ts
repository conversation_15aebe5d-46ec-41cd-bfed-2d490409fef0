import { Var } from '../../var';
import { getRedisClient } from './redisClient';
import { generateSurveyCacheKey } from './cacheKeys';

export const getSurveyFromCache = async (publicKey: string): Promise<any | null> => {
  if (!publicKey) {
    return null;
  }

  try {
    const redisClient = getRedisClient();
    const cacheKey = generateSurveyCacheKey(publicKey);

    const cachedSurvey = await redisClient.get(cacheKey);

    if (!cachedSurvey) {
      return null;
    }

    try {
      const parsedSurvey = JSON.parse(cachedSurvey);

      // Ensure public_key is set in the cached survey
      if (!parsedSurvey.public_key) {
        if (Var.node.env === 'dev') {
          console.error(`${Var.app.emoji.warning} Cached survey missing public_key, adding it:`, publicKey);
        }
        parsedSurvey.public_key = publicKey;
      }

      if (Var.node.env === 'dev') {
        console.log(`${Var.app.emoji.success} Cache hit for survey: ${publicKey}`);
        console.log(`${Var.app.emoji.success} Cached survey data:`, parsedSurvey);
      }

      return parsedSurvey;
    } catch (parseError) {
      console.error(`${Var.app.emoji.failure} Error parsing cached data:`, parseError);
      return null;
    }
  } catch (error) {
    if (Var.node.env === 'dev' || (error instanceof Error && error.message.includes('ECONNREFUSED'))) {
      console.error(`${Var.app.emoji.failure} Redis error:`, error);
    }
    return null;
  }
};
