import Redis from 'ioredis';
import { Var } from '../../var';

let redisClient: Redis | null = null;

export const initRedisClient = (): Redis => {
  if (redisClient) {
    return redisClient;
  }

  try {
    const redisOptions: any = {
      host: Var.redis.host,
      port: Var.redis.port,
      connectTimeout: 10000,
      maxRetriesPerRequest: 3,
      enableReadyCheck: true,
      retryStrategy: (times: number) => {
        const delay = Math.min(times * 1000, 30000);
        return delay;
      },
    };

    if (Var.redis.password) {
      redisOptions.password = Var.redis.password;
    }

    redisClient = new Redis(redisOptions);

    redisClient.on('connect', () => {
      console.log(`${Var.app.emoji.success} Connected to Redis`);
    });

    redisClient.on('error', (error: Error) => {
      console.error(`${Var.app.emoji.failure} Redis error:`, error);
    });

    return redisClient;
  } catch (error) {
    console.error(`${Var.app.emoji.failure} Failed to initialize Redis client:`, error);
    throw error;
  }
};

export const getRedisClient = (): Redis => {
  if (!redisClient) {
    return initRedisClient();
  }
  return redisClient;
};

export const closeRedisConnection = async (): Promise<void> => {
  if (redisClient) {
    await redisClient.quit();
    redisClient = null;
  }
};
