import dotenv from 'dotenv';
import { VarInterface } from '../interfaces';
dotenv.config();

export const Var: VarInterface = {
  app: {
    name: process.env.APP_NAME!,
    emoji: {
      success: '✅',
      failure: '❌',
      warning: '⚠️',
    },
    url: {
      dev: process.env.APP_URL_DEV!,
      prod: process.env.APP_URL_PROD!,
    },
    api: {
      version: process.env.API_VERSION!,
    },
  },
  postgres: {
    host: process.env.POSTGRES_HOST!,
    database: process.env.POSTGRES_DATABASE!,
    user: process.env.POSTGRES_USER!,
    password: process.env.POSTGRES_PASSWORD!,
  },
  redis: {
    host: process.env.REDIS_HOST || 'localhost',
    port: Number(process.env.REDIS_PORT) || 6379,
    password: process.env.REDIS_PASSWORD || undefined,
    cacheTTL: Number(process.env.REDIS_CACHE_TTL) || 86400,
  },
  node: {
    env: process.env.NODE_ENV!,
    port: Number(process.env.NODE_PORT),
  },
};
