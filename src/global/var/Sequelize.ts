import { Sequelize } from 'sequelize';
import { Var } from '.';

// Optimized Sequelize configuration for better performance
// Create Sequelize instance with configuration
export const SequelizeVar = new Sequelize(Var.postgres.database!, Var.postgres.user!, Var.postgres.password!, {
  host: Var.postgres.host,
  dialect: 'postgres',
  // Disable logging in production for better performance
  logging: Var.node.env === 'dev' ? console.log : false,

  // Connection pool configuration for better performance
  pool: {
    max: 20, // Maximum number of connections in pool
    min: 5, // Minimum number of connections in pool
    idle: 10000, // Maximum time, in milliseconds, that a connection can be idle before being released
    acquire: 30000, // Maximum time, in milliseconds, that pool will try to get connection before throwing error
    evict: 1000, // Time interval, in milliseconds, after which sequelize-pool will remove idle connections
  },

  // Query optimization
  define: {
    // Enable timestamps to use created_at and updated_at columns
    timestamps: true,

    // Disable automatic pluralization of table names
    // This ensures model names don't get pluralized automatically
    // We'll explicitly set tableName in each model
    freezeTableName: true,

    // Use underscored style for field names (field_name instead of fieldName)
    // This ensures createdAt becomes created_at and updatedAt becomes updated_at
    underscored: true,
  },

  // Retry configuration
  retry: {
    max: 3, // Maximum number of connection retries
    match: [
      // Only retry on specific errors
      /SequelizeConnectionError/,
      /SequelizeConnectionRefusedError/,
      /SequelizeHostNotFoundError/,
      /SequelizeHostNotReachableError/,
      /SequelizeInvalidConnectionError/,
      /SequelizeConnectionTimedOutError/,
      /TimeoutError/,
    ],
  },

  // Disable benchmark for better performance
  benchmark: false,
});
